{"databases": [{"type": "postgresql", "host": "postgres", "port": 5432, "database": "sanitize_db", "username": "sanitize_user", "password": "sanitize_password", "s3Bucket": "my-backup-bucket", "s3SourcePath": "postgresql/backups", "s3DestinationPath": "postgresql/sanitized", "useLatestBackup": true, "sanitizedFilePrefix": "sanitized"}, {"type": "mysql", "host": "mysql", "port": 3306, "database": "sanitize_db", "username": "sanitize_user", "password": "sanitize_password", "s3Bucket": "my-backup-bucket", "s3SourcePath": "mysql/backups", "s3DestinationPath": "mysql/sanitized", "useLatestBackup": true, "sanitizedFilePrefix": "sanitized"}, {"type": "mysql", "host": "127.0.0.1", "port": 3308, "database": "tracker", "username": "root", "password": "root", "s3Bucket": "tnm-mneme", "s3SourcePath": "prod/hourly/tracker", "s3DestinationPath": "dev/hourly/sanitized/tracker", "useLatestBackup": true, "sanitizedFilePrefix": "sanitized", "enabled": false}, {"type": "postgresql", "host": "127.0.0.1", "port": 5432, "database": "ares", "username": "dev", "password": "dev", "s3Bucket": "tnm-mneme", "s3SourcePath": "dev/hourly/ares", "s3DestinationPath": "dev/hourly/sanitized/ares", "useLatestBackup": true, "sanitizedFilePrefix": "sanitized"}]}