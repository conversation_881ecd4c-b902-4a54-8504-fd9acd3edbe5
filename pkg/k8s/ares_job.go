package k8s

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

// EmbeddedThirdPartyTools contains tools for interacting with third-party services
type EmbeddedThirdPartyTools struct {
	Context          context.Context
	KubernetesClient *kubernetes.Clientset
}

// NewEmbeddedThirdPartyTools creates a new instance of EmbeddedThirdPartyTools
func NewEmbeddedThirdPartyTools(ctx context.Context, k8sClient *kubernetes.Clientset) *EmbeddedThirdPartyTools {
	return &EmbeddedThirdPartyTools{
		Context:          ctx,
		KubernetesClient: k8sClient,
	}
}

// CreateAresSanitizationJob creates a Kubernetes job to sanitize an Ares database
func (t *EmbeddedThirdPartyTools) CreateAresSanitizationJob(dbURL string) error {
	backOffLimit := int32(0)
	name := "ares-sanitization-" + os.Getenv("TIME") // Kubernetes will add a unique identifier to the job name
	namespace := os.Getenv("NAMESPACE")
	ttl := int32(900)

	if namespace == "" {
		namespace = os.Getenv("POD_NAMESPACE")
		if namespace == "" {
			return fmt.Errorf("neither NAMESPACE nor POD_NAMESPACE environment variable is set")
		}
	}

	aresECREndpoint := os.Getenv("ARES_ECR_ENDPOINT")
	if aresECREndpoint == "" {
		return fmt.Errorf("ARES_ECR_ENDPOINT environment variable is not set")
	}

	job := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
		},
		Spec: batchv1.JobSpec{
			BackoffLimit: &backOffLimit,
			Template: corev1.PodTemplateSpec{
				Spec: corev1.PodSpec{
					ServiceAccountName: "mneme",
					NodeSelector: map[string]string{
						"kubernetes.io/arch": "arm64",
					},
					Containers: []corev1.Container{
						{
							ImagePullPolicy: corev1.PullAlways,
							Name:            name,
							Image:           aresECREndpoint + ":latest",
							Command:         []string{"mix"},
							Args:            []string{"sanitize_db", "--sanitize"},
							Env: []corev1.EnvVar{
								// shared envs for both snapshot and instance job
								{
									Name:  "DATABASE_URL",
									Value: dbURL,
								},
							},
						},
					},
					RestartPolicy: corev1.RestartPolicyNever,
				},
			},
			TTLSecondsAfterFinished: &ttl,
		},
	}

	_, err := t.KubernetesClient.BatchV1().Jobs(namespace).Create(t.Context, job, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("failed to create job: %w", err)
	}

	// Poll for the Job completion
	timeout := time.Minute * 15
	pollInterval := time.Second * 5
	startTime := time.Now()

	for {
		// List Jobs with the label selector for this job's prefix
		jobList, err := t.KubernetesClient.BatchV1().Jobs(namespace).List(t.Context, metav1.ListOptions{})
		if err != nil {
			return fmt.Errorf("failed to list jobs: %w", err)
		}

		// Find the job by matching the prefix
		var targetJob *batchv1.Job
		for _, j := range jobList.Items {
			if strings.HasPrefix(j.Name, name) {
				targetJob = &j
				break
			}
		}

		if targetJob == nil {
			return fmt.Errorf("job with prefix %s not found", name)
		}

		// Check Job status conditions
		for _, condition := range targetJob.Status.Conditions {
			if condition.Type == batchv1.JobComplete && condition.Status == corev1.ConditionTrue {
				log.Println("Ares sanitization job completed successfully!")
				return nil
			}
			if condition.Type == batchv1.JobFailed && condition.Status == corev1.ConditionTrue {
				return fmt.Errorf("job failed: %s", condition.Reason)
			}
		}

		// Check for timeout
		if time.Since(startTime) > timeout {
			return fmt.Errorf("timed out waiting for job to complete")
		}

		// Sleep before polling again
		time.Sleep(pollInterval)
	}
}
