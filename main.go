package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/rds"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"

	// Import shared packages
	pkgConfig "github.com/mneme/db-tools/pkg/config"
	"github.com/mneme/db-tools/pkg/db"
	mnemeS3 "github.com/mneme/db-tools/pkg/s3"
)

const (
	awsRegion = "ca-central-1"
)

// sanitizeJobName converts database names to be RFC 1123 compliant for Kubernetes job names
// Replaces underscores with hyphens and converts to lowercase
func sanitizeJobName(dbName string) string {
	return strings.ToLower(strings.ReplaceAll(dbName, "_", "-"))
}

// resolveRDSClusterEndpoint discovers the best endpoint from an RDS cluster
// Prefers reader instances, falls back to writer if no reader is available
func resolveRDSClusterEndpoint(clusterName string) (string, int, error) {
	log.Printf("Resolving RDS cluster endpoint for cluster: %s", clusterName)

	// Describe the cluster to get basic information
	clusterInput := &rds.DescribeDBClustersInput{
		DBClusterIdentifier: aws.String(clusterName),
	}

	clusterResult, err := rdsClient.DescribeDBClusters(context.TODO(), clusterInput)
	if err != nil {
		return "", 0, fmt.Errorf("failed to describe RDS cluster %s: %v", clusterName, err)
	}

	if len(clusterResult.DBClusters) == 0 {
		return "", 0, fmt.Errorf("RDS cluster %s not found", clusterName)
	}

	cluster := clusterResult.DBClusters[0]
	log.Printf("Found RDS cluster %s with %d members", clusterName, len(cluster.DBClusterMembers))

	// Find instances that belong to this cluster
	var readerEndpoint, writerEndpoint string
	var readerPort, writerPort int

	for _, member := range cluster.DBClusterMembers {
		// Get detailed information for this specific instance
		instanceInput := &rds.DescribeDBInstancesInput{
			DBInstanceIdentifier: member.DBInstanceIdentifier,
		}
		instanceResult, err := rdsClient.DescribeDBInstances(context.TODO(), instanceInput)
		if err != nil {
			log.Printf("Warning: failed to describe instance %s: %v", aws.ToString(member.DBInstanceIdentifier), err)
			continue
		}

		if len(instanceResult.DBInstances) == 0 {
			log.Printf("Warning: instance %s not found", aws.ToString(member.DBInstanceIdentifier))
			continue
		}

		instance := instanceResult.DBInstances[0]
		endpoint := aws.ToString(instance.Endpoint.Address)
		port := int(aws.ToInt32(instance.Endpoint.Port))

		log.Printf("Found cluster member %s: endpoint=%s, port=%d, isWriter=%t",
			aws.ToString(member.DBInstanceIdentifier), endpoint, port, aws.ToBool(member.IsClusterWriter))

		if aws.ToBool(member.IsClusterWriter) {
			writerEndpoint = endpoint
			writerPort = port
		} else {
			// This is a reader instance
			readerEndpoint = endpoint
			readerPort = port
		}
	}

	// Prefer reader instance, fallback to writer
	if readerEndpoint != "" && readerPort > 0 {
		log.Printf("Using reader instance endpoint: %s:%d", readerEndpoint, readerPort)
		return readerEndpoint, readerPort, nil
	} else if writerEndpoint != "" && writerPort > 0 {
		log.Printf("No reader instance found, using writer instance endpoint: %s:%d", writerEndpoint, writerPort)
		return writerEndpoint, writerPort, nil
	}

	return "", 0, fmt.Errorf("no available instances with valid endpoints found in RDS cluster %s", clusterName)
}

// resolveDBConfig resolves the database configuration, using RDS cluster discovery if needed
func resolveDBConfig(dbConfig DatabaseConfig) (DatabaseConfig, error) {
	// Create a copy of the config to avoid modifying the original
	resolvedConfig := dbConfig

	// Check if we should use cluster discovery
	clusterName := dbConfig.ClusterName
	if clusterName == "" {
		// If ClusterName is not set, check if Host looks like a cluster name
		// This is for backward compatibility where Host might contain cluster names
		clusterName = dbConfig.Host
	}

	// Only attempt cluster discovery for main backup jobs (not sanitize jobs)
	// Main backup jobs run in normal mode and create single-mode jobs
	// Single-mode jobs should use the resolved endpoint passed from the main job
	if clusterName != "" && !singleMode {
		log.Printf("Attempting to resolve %s as RDS cluster for main backup job", clusterName)
		endpoint, port, err := resolveRDSClusterEndpoint(clusterName)
		if err != nil {
			log.Printf("Failed to resolve as RDS cluster, using as direct endpoint: %v", err)
			// Fall back to using the original host/port
			return resolvedConfig, nil
		}

		// Update the config with the resolved endpoint
		resolvedConfig.Host = endpoint
		if port > 0 {
			resolvedConfig.Port = port
		}
		log.Printf("Resolved cluster %s to endpoint %s:%d for backup", clusterName, endpoint, resolvedConfig.Port)

		// Validate that we have a valid endpoint
		if resolvedConfig.Host == "" {
			return resolvedConfig, fmt.Errorf("resolved endpoint is empty for cluster %s", clusterName)
		}
	} else if clusterName != "" && singleMode {
		log.Printf("Single mode detected - using provided endpoint %s for cluster %s", resolvedConfig.Host, clusterName)
		// In single mode, the main job has already resolved the cluster to a specific endpoint
		// We should use the provided Host value directly
	}

	return resolvedConfig, nil
}

var (
	awsConfig  aws.Config
	smClient   *secretsmanager.Client
	s3Client   *s3.Client
	rdsClient  *rds.Client
	k8sClient  *kubernetes.Clientset
	localMode  bool
	singleMode bool
	logLevel   string
)

func init() {
	log.Println("Starting mneme initialization")

	flag.BoolVar(&localMode, "local", false, "Run in local mode (without Kubernetes)")
	flag.BoolVar(&singleMode, "single", false, "Run a single database backup with configuration from environment variables")
	flag.Parse()

	logLevel = os.Getenv("LOG_LEVEL")
	if logLevel == "debug" {
		log.Printf("Starting in debug mode")
	}
}

func initializeClients() error {
	// Initialize AWS config for S3 access
	var err error
	log.Println("Loading AWS configuration...")

	// Load AWS config
	awsConfig, err = config.LoadDefaultConfig(context.TODO(),
		config.WithRegion(awsRegion),
	)
	if err != nil {
		return fmt.Errorf("unable to load AWS SDK config: %v", err)
	}
	log.Println("AWS configuration loaded successfully")

	// Initialize Secrets Manager client
	log.Println("Initializing Secrets Manager client...")
	smClient = secretsmanager.NewFromConfig(awsConfig)
	log.Println("Secrets Manager client initialized")

	// Initialize S3 client
	log.Println("Initializing S3 client...")
	s3Client = s3.NewFromConfig(awsConfig)
	log.Println("S3 client initialized")

	// Initialize RDS client
	log.Println("Initializing RDS client...")
	rdsClient = rds.NewFromConfig(awsConfig)
	log.Println("RDS client initialized")

	// Initialize Kubernetes client if not in local or single mode
	if !localMode && !singleMode {
		log.Println("Initializing Kubernetes client...")
		k8sConfig, err := rest.InClusterConfig()
		if err != nil {
			return fmt.Errorf("unable to create Kubernetes config: %v", err)
		}

		k8sClient, err = kubernetes.NewForConfig(k8sConfig)
		if err != nil {
			return fmt.Errorf("unable to create Kubernetes client: %v", err)
		}
		log.Println("Kubernetes client initialized successfully")
	} else if localMode {
		log.Println("Running in local mode, skipping Kubernetes client initialization")
	}

	return nil
}

func getConfigFromSecretsManager(secretARN string) (*Config, error) {
	log.Printf("Fetching configuration from Secrets Manager: %s", secretARN)
	input := &secretsmanager.GetSecretValueInput{
		SecretId: &secretARN,
	}

	result, err := smClient.GetSecretValue(context.TODO(), input)
	if err != nil {
		return nil, fmt.Errorf("failed to get secret value: %v", err)
	}
	log.Println("Successfully retrieved secret from Secrets Manager")

	var config Config
	if err := json.Unmarshal([]byte(*result.SecretString), &config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %v", err)
	}
	log.Printf("Successfully parsed configuration for %d databases", len(config.Databases))
	return &config, nil
}

// Convert our DatabaseConfig to the shared package's BackupDatabaseConfig
func convertToBackupDatabaseConfig(dbConfig DatabaseConfig) pkgConfig.BackupDatabaseConfig {
	return pkgConfig.BackupDatabaseConfig{
		DatabaseConfig: pkgConfig.DatabaseConfig{
			Type:        dbConfig.Type,
			Host:        dbConfig.Host,
			ClusterName: dbConfig.ClusterName,
			Port:        dbConfig.Port,
			Database:    dbConfig.Database,
			Username:    dbConfig.Username,
			Password:    dbConfig.Password,
			S3Bucket:    dbConfig.S3Bucket,
			Enabled:     dbConfig.Enabled,
		},
		S3Path: dbConfig.S3Path,
	}
}

func performBackup(dbConfig DatabaseConfig) error {
	log.Printf("Starting backup for database %s", dbConfig.Database)

	// Resolve the database configuration (RDS cluster discovery if needed)
	resolvedConfig, err := resolveDBConfig(dbConfig)
	if err != nil {
		return fmt.Errorf("failed to resolve database configuration: %v", err)
	}

	// Convert to shared package config format
	sharedConfig := convertToBackupDatabaseConfig(resolvedConfig)

	var backupFile string

	if dbConfig.Type == "postgresql" {
		backupFile, err = db.PostgresBackup(sharedConfig)
	} else if dbConfig.Type == "mysql" {
		backupFile, err = db.MySQLBackup(sharedConfig)
	} else {
		return fmt.Errorf("unsupported database type: %s", dbConfig.Type)
	}

	if err != nil {
		return fmt.Errorf("failed to create backup: %v", err)
	}

	log.Printf("Backup completed for database %s: %s", dbConfig.Database, backupFile)

	// Upload to S3
	log.Printf("Uploading to S3: s3://%s/%s", dbConfig.S3Bucket, dbConfig.S3Path)
	if err := mnemeS3.UploadFile(s3Client, dbConfig.S3Bucket, dbConfig.S3Path, backupFile); err != nil {
		return fmt.Errorf("failed to upload to S3: %v", err)
	}

	// Clean up local file
	if err := os.Remove(backupFile); err != nil {
		log.Printf("Warning: failed to delete local backup file %s: %v", backupFile, err)
	} else {
		log.Printf("Cleaned up local backup file: %s", backupFile)
	}

	return nil
}

func createBackupJob(dbConfig DatabaseConfig) (*batchv1.Job, error) {
	jobName := fmt.Sprintf("mneme-%s-%s", sanitizeJobName(dbConfig.Database), time.Now().Format("20060102150405"))
	log.Printf("Creating backup job: %s", jobName)

	// Resolve the database configuration (RDS cluster discovery if needed)
	resolvedConfig, err := resolveDBConfig(dbConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve database configuration: %v", err)
	}

	// Get the parent pod's image
	parentImage := os.Getenv("HOSTNAME")
	if parentImage == "" {
		return nil, fmt.Errorf("unable to get parent pod name")
	}

	// Get the pod to find its image
	pod, err := k8sClient.CoreV1().Pods(os.Getenv("POD_NAMESPACE")).Get(context.TODO(), parentImage, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("unable to get parent pod: %v", err)
	}

	// Find the mneme container's image
	var image string
	for _, container := range pod.Spec.Containers {
		if container.Name == "mneme" {
			image = container.Image
			break
		}
	}
	if image == "" {
		return nil, fmt.Errorf("unable to find mneme container image in parent pod")
	}

	job := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      jobName,
			Namespace: os.Getenv("POD_NAMESPACE"),
		},
		Spec: batchv1.JobSpec{
			TTLSecondsAfterFinished: aws.Int32(1800), // Delete succeeded jobs after 30 minutes
			Template: corev1.PodTemplateSpec{
				Spec: corev1.PodSpec{
					ServiceAccountName: "mneme",
					Containers: []corev1.Container{
						{
							Name:            "mneme",
							Image:           image,
							ImagePullPolicy: corev1.PullPolicy("IfNotPresent"),
							Command:         []string{"/usr/local/bin/mneme"},
							Args:            []string{"-single"},
							Env: []corev1.EnvVar{
								{
									Name:  "AWS_REGION",
									Value: awsRegion,
								},
								{
									Name:  "DB_TYPE",
									Value: resolvedConfig.Type,
								},
								{
									Name:  "DB_HOST",
									Value: resolvedConfig.Host,
								},
								{
									Name:  "DB_CLUSTER_NAME",
									Value: resolvedConfig.ClusterName,
								},
								{
									Name:  "DB_PORT",
									Value: fmt.Sprintf("%d", resolvedConfig.Port),
								},
								{
									Name:  "DB_NAME",
									Value: resolvedConfig.Database,
								},
								{
									Name:  "DB_USERNAME",
									Value: resolvedConfig.Username,
								},
								{
									Name:  "DB_PASSWORD",
									Value: resolvedConfig.Password,
								},
								{
									Name:  "S3_BUCKET",
									Value: resolvedConfig.S3Bucket,
								},
								{
									Name:  "S3_PATH",
									Value: resolvedConfig.S3Path,
								},
							},
						},
					},
					RestartPolicy: corev1.RestartPolicyOnFailure,
				},
			},
		},
	}

	return k8sClient.BatchV1().Jobs(os.Getenv("POD_NAMESPACE")).Create(context.TODO(), job, metav1.CreateOptions{})
}

func main() {
	// Set up logging to ensure it's flushed
	log.SetFlags(log.LstdFlags | log.Lmicroseconds)
	log.SetOutput(os.Stdout)

	log.Println("Starting mneme main execution")
	log.Printf("Command line arguments: %v", os.Args)

	// Initialize clients
	if err := initializeClients(); err != nil {
		log.Fatalf("Failed to initialize clients: %v", err)
	}

	if singleMode {
		// Check for required environment variables first
		var missingVars []string
		if os.Getenv("DB_TYPE") == "" {
			missingVars = append(missingVars, "DB_TYPE")
		}
		// Either DB_HOST or DB_CLUSTER_NAME must be provided
		if os.Getenv("DB_HOST") == "" && os.Getenv("DB_CLUSTER_NAME") == "" {
			missingVars = append(missingVars, "DB_HOST or DB_CLUSTER_NAME")
		}
		if os.Getenv("DB_NAME") == "" {
			missingVars = append(missingVars, "DB_NAME")
		}
		if os.Getenv("DB_USERNAME") == "" {
			missingVars = append(missingVars, "DB_USERNAME")
		}
		if os.Getenv("DB_PASSWORD") == "" {
			missingVars = append(missingVars, "DB_PASSWORD")
		}
		if os.Getenv("S3_BUCKET") == "" {
			missingVars = append(missingVars, "S3_BUCKET")
		}
		if os.Getenv("S3_PATH") == "" {
			missingVars = append(missingVars, "S3_PATH")
		}
		if os.Getenv("DB_PORT") == "" {
			missingVars = append(missingVars, "DB_PORT")
		}

		if len(missingVars) > 0 {
			log.Fatalf("Missing required environment variables for single mode: %v", missingVars)
		}

		dbConfig := DatabaseConfig{
			Type:        os.Getenv("DB_TYPE"),
			Host:        os.Getenv("DB_HOST"),
			ClusterName: os.Getenv("DB_CLUSTER_NAME"),
			Port:        func() int { p, _ := strconv.Atoi(os.Getenv("DB_PORT")); return p }(),
			Database:    os.Getenv("DB_NAME"),
			Username:    os.Getenv("DB_USERNAME"),
			Password:    os.Getenv("DB_PASSWORD"),
			S3Bucket:    os.Getenv("S3_BUCKET"),
			S3Path:      os.Getenv("S3_PATH"),
		}

		log.Printf("Running single backup for database %s", dbConfig.Database)
		if err := performBackup(dbConfig); err != nil {
			log.Fatalf("Failed to backup database %s: %v", dbConfig.Database, err)
		}
		return
	}

	// Normal mode - fetch from Secrets Manager
	if len(os.Args) < 2 {
		log.Fatal("Usage: mneme <secret-arn>")
	}

	// Get the secret ARN (last argument)
	secretARN := os.Args[len(os.Args)-1]
	log.Printf("Using secret ARN: %s", secretARN)

	config, err := getConfigFromSecretsManager(secretARN)
	if err != nil {
		log.Fatalf("Failed to get config: %v", err)
	}

	log.Printf("Starting backups for %d databases", len(config.Databases))
	for _, dbConfig := range config.Databases {
		// Check if the database is enabled
		if dbConfig.Enabled != nil && !*dbConfig.Enabled {
			log.Printf("Skipping disabled database %s", dbConfig.Database)
			continue
		}

		if localMode {
			// Run locally without Kubernetes
			log.Printf("Running local backup for database %s", dbConfig.Database)
			if err := performBackup(dbConfig); err != nil {
				log.Printf("Failed to backup database %s: %v", dbConfig.Database, err)
				continue
			}
		} else {
			// Create Kubernetes job
			log.Printf("Creating Kubernetes job for database %s", dbConfig.Database)
			job, err := createBackupJob(dbConfig)
			if err != nil {
				log.Printf("Failed to create job for database %s: %v", dbConfig.Database, err)
				continue
			}
			log.Printf("Created backup job %s for database %s", job.Name, dbConfig.Database)
		}
	}
	log.Println("mneme execution completed")
}
