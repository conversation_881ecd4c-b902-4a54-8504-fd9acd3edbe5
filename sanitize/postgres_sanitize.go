package sanitize

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	_ "github.com/lib/pq"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"

	"github.com/mneme/db-tools/pkg/k8s"
)

// PostgresSanitizer handles sanitization of PostgreSQL databases
type PostgresSanitizer struct {
	Host     string
	Port     int
	Database string
	Username string
	Password string
	// Kubernetes client for creating Ares sanitization jobs
	K8sClient *kubernetes.Clientset
}

// NewPostgresSanitizer creates a new PostgreSQL sanitizer
func NewPostgresSanitizer(host string, port int, database, username, password string) *PostgresSanitizer {
	// Try to initialize Kubernetes client if we're running in a cluster
	var k8sClient *kubernetes.Clientset
	if os.Getenv("KUBERNETES_SERVICE_HOST") != "" {
		config, err := rest.InClusterConfig()
		if err == nil {
			client, err := kubernetes.NewForConfig(config)
			if err == nil {
				k8sClient = client
			} else {
				log.Printf("Warning: Failed to create Kubernetes client: %v", err)
			}
		} else {
			log.Printf("Warning: Failed to get in-cluster config: %v", err)
		}
	}

	return &PostgresSanitizer{
		Host:      host,
		Port:      port,
		Database:  database,
		Username:  username,
		Password:  password,
		K8sClient: k8sClient,
	}
}

// Connect establishes a connection to the PostgreSQL database
func (s *PostgresSanitizer) Connect() (*sql.DB, error) {
	connStr := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable",
		s.Host, s.Port, s.Username, s.Password, s.Database)

	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to PostgreSQL: %v", err)
	}

	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping PostgreSQL: %v", err)
	}

	return db, nil
}

// GetDatabaseURL returns the database URL for connecting to the PostgreSQL database
func (s *PostgresSanitizer) GetDatabaseURL() string {
	return fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable",
		s.Username, s.Password, s.Host, s.Port, s.Database)
}

// Sanitize performs sanitization on the PostgreSQL database
func (s *PostgresSanitizer) Sanitize() error {
	db, err := s.Connect()
	if err != nil {
		return err
	}
	defer db.Close()

	log.Printf("Starting PostgreSQL database sanitization check for database: %s", s.Database)

	// Check if the database name contains "ares"
	if !strings.Contains(strings.ToLower(s.Database), "ares") {
		log.Printf("Skipping sanitization for PostgreSQL database %s (not an ares database)", s.Database)
		return nil
	}

	log.Printf("Database %s contains 'ares', proceeding with sanitization", s.Database)

	// For Ares databases, we need to create a Kubernetes job to perform the sanitization
	if s.K8sClient != nil {
		log.Printf("Creating Ares sanitization job for database %s", s.Database)
		
		// Set the TIME environment variable if not already set
		if os.Getenv("TIME") == "" {
			os.Setenv("TIME", time.Now().Format("20060102150405"))
		}
		
		// Create the tools instance
		tools := k8s.NewEmbeddedThirdPartyTools(context.Background(), s.K8sClient)
		
		// Get the database URL
		dbURL := s.GetDatabaseURL()
		
		// Create the sanitization job
		if err := tools.CreateAresSanitizationJob(dbURL); err != nil {
			return fmt.Errorf("failed to create Ares sanitization job: %v", err)
		}
		
		log.Printf("Ares sanitization job completed for database %s", s.Database)
	} else {
		log.Printf("Kubernetes client not available, skipping Ares sanitization for database %s", s.Database)
		log.Printf("WARNING: Ares database %s was not sanitized", s.Database)
	}

	log.Println("PostgreSQL database sanitization completed successfully")
	return nil
}
